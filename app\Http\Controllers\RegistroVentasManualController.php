<?php

namespace App\Http\Controllers;

use App\Models\Products;
use Illuminate\Http\Request;

class RegistroVentasManualController extends Controller
{
    public function search(Request $request)
    {

        $products =  Products::where("status", "=", true)->where('parent_id', '=', null)->where('producto', 'like', "%$request->search%")->with(['images', 'tipos', 'componentesHijos.tipos'])->get();
        $ids = $products->pluck('id')->toArray();

        // $subproductos = Products::whereIn('parent_id', $ids)->with(['images', 'tipos', 'especificaciones'])->get();

        return response()->json([
            'status' => 'success',
            'message' => 'Registro de ventas manual',
            'data' => [
                'products' => $products,
                // 'subproductos' => $subproductos
            ]
        ]);
    }
}
